import { app } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import * as crypto from 'crypto'

export interface AccountInfo {
  id: string
  displayName: string
  userPrincipalName: string
  mail: string
  jobTitle?: string
  officeLocation?: string
  businessPhones: string[]
  mobilePhone?: string
  photo?: string
  driveType?: string
  lastLoginTime: number
  isActive: boolean
}

export interface StoredAccount {
  id: string
  accountInfo: AccountInfo
  encryptedTokens: string
  refreshToken?: string
  expiresAt?: number
}

export class AccountService {
  private accountsPath: string
  private accounts: Map<string, StoredAccount> = new Map()
  private encryptionKey: string

  constructor() {
    this.accountsPath = this.getAccountsPath()
    this.encryptionKey = this.getOrCreateEncryptionKey()
    this.loadAccounts()
  }

  private getAccountsPath(): string {
    const userDataPath = app.getPath('userData')
    const accountsDir = path.join(userDataPath, 'accounts')
    
    // 确保账号目录存在
    if (!fs.existsSync(accountsDir)) {
      fs.mkdirSync(accountsDir, { recursive: true })
    }
    
    return path.join(accountsDir, 'accounts.json')
  }

  private getOrCreateEncryptionKey(): string {
    const keyPath = path.join(app.getPath('userData'), 'accounts', '.key')
    
    try {
      if (fs.existsSync(keyPath)) {
        return fs.readFileSync(keyPath, 'utf-8')
      } else {
        // 确保目录存在
        const keyDir = path.dirname(keyPath)
        if (!fs.existsSync(keyDir)) {
          fs.mkdirSync(keyDir, { recursive: true })
        }
        
        // 生成新的加密密钥
        const key = crypto.randomBytes(32).toString('hex')
        fs.writeFileSync(keyPath, key, 'utf-8')
        return key
      }
    } catch (error) {
      console.error('Failed to handle encryption key:', error)
      // 使用机器标识符作为备用密钥
      return crypto.createHash('sha256').update(app.getPath('userData')).digest('hex')
    }
  }

  private encrypt(text: string): string {
    try {
      const algorithm = 'aes-256-cbc'
      const key = Buffer.from(this.encryptionKey, 'hex')
      const iv = crypto.randomBytes(16)
      
      const cipher = crypto.createCipheriv(algorithm, key, iv)
      let encrypted = cipher.update(text, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      
      return iv.toString('hex') + ':' + encrypted
    } catch (error) {
      console.error('Encryption failed:', error)
      return text // If encryption fails, return original text (not secure, but keeps functionality)
    }
  }

  private decrypt(encryptedText: string): string {
    try {
      const algorithm = 'aes-256-cbc'
      const key = Buffer.from(this.encryptionKey, 'hex')
      
      const textParts = encryptedText.split(':')
      const iv = Buffer.from(textParts.shift()!, 'hex')
      const encryptedData = textParts.join(':')
      
      const decipher = crypto.createDecipheriv(algorithm, key, iv)
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      
      return decrypted
    } catch (error) {
      console.error('Decryption failed:', error)
      return encryptedText // If decryption fails, return original text
    }
  }

  private loadAccounts(): void {
    try {
      if (fs.existsSync(this.accountsPath)) {
        const accountsData = fs.readFileSync(this.accountsPath, 'utf-8')
        const accountsArray: StoredAccount[] = JSON.parse(accountsData)
        
        this.accounts.clear()
        accountsArray.forEach(account => {
          this.accounts.set(account.id, account)
        })
        
        console.log(`Loaded ${this.accounts.size} accounts`)
      }
    } catch (error) {
      console.error('Failed to load accounts:', error)
      this.accounts.clear()
    }
  }

  private saveAccounts(): void {
    try {
      const accountsArray = Array.from(this.accounts.values())
      const accountsData = JSON.stringify(accountsArray, null, 2)
      
      const accountsDir = path.dirname(this.accountsPath)
      if (!fs.existsSync(accountsDir)) {
        fs.mkdirSync(accountsDir, { recursive: true })
      }
      
      fs.writeFileSync(this.accountsPath, accountsData, 'utf-8')
      console.log('Account information saved')
    } catch (error) {
      console.error('Failed to save accounts:', error)
      throw new Error('Failed to save accounts')
    }
  }

  public addAccount(accountInfo: AccountInfo, accessToken: string, refreshToken?: string, expiresAt?: number): string {
    const accountId = accountInfo.id
    
    // 加密令牌
    const tokenData = {
      accessToken,
      refreshToken,
      expiresAt
    }
    const encryptedTokens = this.encrypt(JSON.stringify(tokenData))
    
    // 创建存储账号
    const storedAccount: StoredAccount = {
      id: accountId,
      accountInfo: {
        ...accountInfo,
        lastLoginTime: Date.now(),
        isActive: true
      },
      encryptedTokens,
      refreshToken,
      expiresAt
    }
    
    // 将其他账号（除了当前账号）设为非活跃状态
    this.accounts.forEach((account, id) => {
      if (id !== accountId) {
        account.accountInfo.isActive = false
      }
    })
    
    this.accounts.set(accountId, storedAccount)
    this.saveAccounts()
    
    console.log('Account added:', accountInfo.displayName)
    return accountId
  }

  public getAccount(accountId: string): StoredAccount | null {
    return this.accounts.get(accountId) || null
  }

  public getAllAccounts(): AccountInfo[] {
    return Array.from(this.accounts.values())
      .map(account => account.accountInfo)
      .sort((a, b) => b.lastLoginTime - a.lastLoginTime)
  }

  public getActiveAccount(): StoredAccount | null {
    for (const account of this.accounts.values()) {
      if (account.accountInfo.isActive) {
        return account
      }
    }
    return null
  }

  public switchAccount(accountId: string): boolean {
    const account = this.accounts.get(accountId)
    if (!account) {
      return false
    }
    
    // 将所有账号设为非活跃状态
    this.accounts.forEach(acc => {
      acc.accountInfo.isActive = false
    })
    
    // 激活指定账号
    account.accountInfo.isActive = true
    account.accountInfo.lastLoginTime = Date.now()
    
    this.saveAccounts()
    console.log('Switched to account:', account.accountInfo.displayName)
    return true
  }

  public removeAccount(accountId: string): boolean {
    const account = this.accounts.get(accountId)
    if (!account) {
      return false
    }
    
    this.accounts.delete(accountId)
    this.saveAccounts()
    
    console.log('Account removed:', account.accountInfo.displayName)
    return true
  }

  public getDecryptedTokens(accountId: string): { accessToken: string; refreshToken?: string; expiresAt?: number } | null {
    const account = this.accounts.get(accountId)
    if (!account) {
      return null
    }
    
    try {
      const decryptedData = this.decrypt(account.encryptedTokens)
      return JSON.parse(decryptedData)
    } catch (error) {
      console.error('Failed to decrypt tokens:', error)
      return null
    }
  }

  public updateAccountTokens(accountId: string, accessToken: string, refreshToken?: string, expiresAt?: number): boolean {
    const account = this.accounts.get(accountId)
    if (!account) {
      return false
    }
    
    const tokenData = {
      accessToken,
      refreshToken,
      expiresAt
    }
    
    account.encryptedTokens = this.encrypt(JSON.stringify(tokenData))
    account.refreshToken = refreshToken
    account.expiresAt = expiresAt
    account.accountInfo.lastLoginTime = Date.now()
    
    this.saveAccounts()
    return true
  }

  public updateAccountInfo(accountId: string, accountInfo: Partial<AccountInfo>): boolean {
    const account = this.accounts.get(accountId)
    if (!account) {
      return false
    }
    
    account.accountInfo = { ...account.accountInfo, ...accountInfo }
    this.saveAccounts()
    return true
  }

  public clearAllAccounts(): void {
    this.accounts.clear()
    this.saveAccounts()
    console.log('All accounts cleared')
  }

  public isTokenExpired(accountId: string): boolean {
    const account = this.accounts.get(accountId)
    if (!account || !account.expiresAt) {
      return true
    }
    
    return Date.now() >= account.expiresAt
  }
} 