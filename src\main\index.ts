import { app, shell, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join } from 'path'
import { AuthService } from './services/authService'
import { GraphService } from './services/graphService'
import { ConfigService } from './services/configService'
import { AccountService } from './services/accountService'
import { APP_CONFIG } from './config'

// Initialize services
const configService = new ConfigService()
const accountService = new AccountService()
const authService = new AuthService(APP_CONFIG.auth)
const graphService = new GraphService(authService)

function createWindow(): void {
  const config = configService.getConfig()
  
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: config.windowBounds.width,
    height: config.windowBounds.height,
    x: config.windowBounds.x,
    y: config.windowBounds.y,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,
    icon: join(__dirname, '../../resources/icon.png'),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: false
    }
  })

  // 保存窗口位置和大小
  mainWindow.on('close', () => {
    const bounds = mainWindow.getBounds()
    configService.updateConfig({
      windowBounds: {
        width: bounds.width,
        height: bounds.height,
        x: bounds.x,
        y: bounds.y
      }
    })
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
    // 只有在开发者模式开启时才自动打开开发者工具
    const config = configService.getConfig()
    if (process.env.NODE_ENV === 'development' && config.developerMode) {
      mainWindow.webContents.openDevTools()
    }
  })



  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 添加快捷键支持
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // 检查是否是开发者工具快捷键
    const isDevToolsShortcut = (input.control && input.shift && input.key.toLowerCase() === 'i') || input.key === 'F12'
    
    if (isDevToolsShortcut) {
      // 只有在开发者模式开启时才允许打开开发者工具
      const config = configService.getConfig()
      console.log('Developer tools shortcut triggered, developer mode status:', config.developerMode)
      
      if (config.developerMode) {
        mainWindow.webContents.toggleDevTools()
      } else {
        console.log('Developer mode not enabled, ignoring developer tools shortcut')
        event.preventDefault()
      }
    }
  })

  // Load the remote URL for development or the local html file for production.
  if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.whenReady().then(() => {
  // Set app user model id for windows
  app.setAppUserModelId('com.od-commander')

  // IPC test
  ipcMain.handle('ping', () => 'pong')

  // Dialog handlers
  ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
    const result = await dialog.showSaveDialog(options)
    return result
  })

  ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
    const result = await dialog.showOpenDialog(options)
    return result
  })

  // Config handlers
  ipcMain.handle('config:get', () => {
    return configService.getConfig()
  })

  ipcMain.handle('config:update', (event, updates) => {
    try {
      configService.updateConfig(updates)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  })

  ipcMain.handle('config:reset', () => {
    try {
      configService.resetConfig()
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : String(error) }
    }
  })

  ipcMain.handle('config:get-path', () => {
    return configService.getConfigPath()
  })

  ipcMain.handle('config:set-custom-path', (event, customPath) => {
    try {
      configService.setCustomConfigPath(customPath)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('config:export', (event, exportPath) => {
    try {
      configService.exportConfig(exportPath)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('config:import', (event, importPath) => {
    try {
      configService.importConfig(importPath)
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('config:clear-cache', () => {
    try {
      configService.clearCache()
      return { success: true }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// IPC handlers for authentication and file operations
ipcMain.handle('auth:login', async (event, forceNewLogin: boolean = false) => {
  try {
    const result = await authService.authenticate(forceNewLogin)
    const userInfo = await graphService.getUserInfo()
    const userProfile = await graphService.getUserProfile()
    
    // 检查是否启用了记住登录
    const config = configService.getConfig()
    if (config.rememberLogin) {
      try {
        // 保存账号信息
        const accountInfo = {
          id: userProfile.id,
          displayName: userProfile.displayName,
          userPrincipalName: userProfile.userPrincipalName,
          mail: userProfile.mail,
          jobTitle: userProfile.jobTitle,
          officeLocation: userProfile.officeLocation,
          businessPhones: userProfile.businessPhones,
          mobilePhone: userProfile.mobilePhone,
          photo: userProfile.photo,
          lastLoginTime: Date.now(),
          isActive: true
        }
        
        const accountId = accountService.addAccount(
          accountInfo,
          result.accessToken,
          undefined, // MSAL不直接提供refreshToken
          result.expiresOn ? result.expiresOn.getTime() : undefined
        )
        
        // 确保配置同步：更新配置中的当前账号ID
        configService.updateConfig({ currentAccountId: accountId })
      } catch (accountError) {
        console.error('Failed to save account information:', accountError)
        // 即使保存账号失败，登录仍然成功，只是不会记住登录状态
      }
    }
    
    return {
      success: true,
      accessToken: result.accessToken,
      userInfo: userInfo,
      profile: userProfile
    }
  } catch (error) {
    console.error('Authentication failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Authentication failed'
    }
  }
})

ipcMain.handle('auth:logout', async () => {
  try {
    await authService.logout()
    
    // 清除当前账号ID
    configService.updateConfig({ currentAccountId: undefined })
    
    return { success: true }
  } catch (error) {
    console.error('Logout failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Logout failed'
    }
  }
})

// 自动登录
ipcMain.handle('auth:auto-login', async () => {
  try {
    const config = configService.getConfig()
    
    if (!config.rememberLogin) {
      return { success: false, error: 'Remember login not enabled' }
    }
    
    // 优先使用AccountService的活跃账号
    let activeAccount = accountService.getActiveAccount()
    let accountId = activeAccount?.id
    
    // 如果AccountService没有活跃账号，但配置中有currentAccountId，尝试使用配置中的账号
    if (!accountId && config.currentAccountId) {
      const configAccount = accountService.getAccount(config.currentAccountId)
      if (configAccount) {
        // 修复状态不一致问题：将配置中的账号设为活跃
        accountService.switchAccount(config.currentAccountId)
        activeAccount = configAccount
        accountId = config.currentAccountId
      }
    }
    
    if (!accountId || !activeAccount) {
      return { success: false, error: 'No current account' }
    }
    
    const tokens = accountService.getDecryptedTokens(accountId)
    if (!tokens) {
      return { success: false, error: 'Unable to get tokens' }
    }
    
    // 检查令牌是否过期
    if (accountService.isTokenExpired(accountId)) {
      // 尝试刷新令牌
      if (tokens.refreshToken) {
        try {
          const refreshResult = await authService.refreshToken(tokens.refreshToken)
          
          // 更新令牌
          accountService.updateAccountTokens(
            accountId,
            refreshResult.accessToken,
            refreshResult.refreshToken,
            refreshResult.expiresAt
          )
          
          // 设置新令牌到认证服务
          authService.setAccessToken(refreshResult.accessToken)
          
          // 确保配置同步
          configService.updateConfig({ currentAccountId: accountId })
          
          return {
            success: true,
            accessToken: refreshResult.accessToken,
            userInfo: activeAccount.accountInfo,
            profile: activeAccount.accountInfo,
            isAutoLogin: true
          }
        } catch (refreshError) {
          console.error('Failed to refresh token:', refreshError)
          return { success: false, error: 'Token expired, please log in again' }
        }
      } else {
        return { success: false, error: 'Token expired, please log in again' }
      }
    }
    
    // 令牌有效，直接使用
    authService.setAccessToken(tokens.accessToken)
    
    // 确保配置同步
    configService.updateConfig({ currentAccountId: accountId })
    
    return {
      success: true,
      accessToken: tokens.accessToken,
      userInfo: activeAccount.accountInfo,
      profile: activeAccount.accountInfo,
      isAutoLogin: true
    }
  } catch (error) {
    console.error('Auto login failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Auto login failed'
    }
  }
})

ipcMain.handle('files:list', async (event, path: string = '') => {
  try {
    const items = await graphService.getDriveItems(path)
    return {
      success: true,
      items: items
    }
  } catch (error) {
    console.error('Failed to list files:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to list files'
    }
  }
})

ipcMain.handle('files:create-folder', async (event, name: string, parentPath: string = '') => {
  try {
    const folder = await graphService.createFolder(name, parentPath)
    return {
      success: true,
      folder: folder
    }
  } catch (error) {
    console.error('Failed to create folder:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create folder'
    }
  }
})

ipcMain.handle('files:upload', async (event, filePath: string, fileName: string, parentPath: string = '') => {
  try {
    const file = await graphService.uploadFile(filePath, fileName, parentPath)
    return {
      success: true,
      file: file
    }
  } catch (error) {
    console.error('Failed to upload file:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload file'
    }
  }
})

ipcMain.handle('files:upload-buffer', async (event, fileData: number[], fileName: string, parentPath: string = '') => {
  try {
    const buffer = Buffer.from(fileData)
    const file = await graphService.uploadFileBuffer(buffer, fileName, parentPath)
    return {
      success: true,
      file: file
    }
  } catch (error) {
    console.error('Failed to upload file buffer:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload file buffer'
    }
  }
})

ipcMain.handle('files:download', async (event, itemId: string, localPath: string) => {
  try {
    await graphService.downloadFile(itemId, localPath)
    return { success: true }
  } catch (error) {
    console.error('Failed to download file:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to download file'
    }
  }
})

ipcMain.handle('files:delete', async (event, itemId: string) => {
  try {
    await graphService.deleteItem(itemId)
    return { success: true }
  } catch (error) {
    console.error('Failed to delete item:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete item'
    }
  }
})

ipcMain.handle('files:search', async (event, query: string) => {
  try {
    const items = await graphService.searchFiles(query)
    return {
      success: true,
      items: items
    }
  } catch (error) {
    console.error('Failed to search files:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to search files'
    }
  }
})

ipcMain.handle('user:get-storage-info', async () => {
  try {
    const storageInfo = await graphService.getStorageInfo()
    return {
      success: true,
      storageInfo: storageInfo
    }
  } catch (error) {
    console.error('Failed to get storage info:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get storage info'
    }
  }
})

ipcMain.handle('user:get-profile', async () => {
  try {
    const profile = await graphService.getUserProfile()
    return {
      success: true,
      profile: profile
    }
  } catch (error) {
    console.error('Failed to get user profile:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user profile'
    }
  }
})

ipcMain.handle('user:get-photo', async (event, size = '120x120') => {
  try {
    const photo = await graphService.getUserPhoto(size)
    return {
      success: true,
      photo: photo
    }
  } catch (error) {
    console.error('Failed to get user photo:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user photo'
    }
  }
})



// 账号管理相关的IPC处理器
ipcMain.handle('accounts:get-all', () => {
  try {
    const accounts = accountService.getAllAccounts()
    const config = configService.getConfig()
    
    console.log('All accounts:', accounts.map(a => `${a.displayName} (${a.id})`))
    console.log('Current account ID:', config.currentAccountId)
    
    // 使用AccountService内部的isActive状态，确保状态一致性
    return { success: true, accounts: accounts }
  } catch (error) {
    console.error('Failed to get accounts list:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Failed to get accounts list' }
  }
})

ipcMain.handle('accounts:get-active', () => {
  try {
    const activeAccount = accountService.getActiveAccount()
    console.log('Active account:', activeAccount?.accountInfo?.displayName)
    
    return { success: true, account: activeAccount?.accountInfo || null }
  } catch (error) {
    console.error('Failed to get active account:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Failed to get active account' }
  }
})

ipcMain.handle('accounts:switch', async (event, accountId: string) => {
  try {
    const success = accountService.switchAccount(accountId)
    if (!success) {
      return { success: false, error: 'Account not found' }
    }
    
    // 获取账号令牌
    const tokens = accountService.getDecryptedTokens(accountId)
    if (!tokens) {
      return { success: false, error: 'Unable to get account tokens' }
    }
    
    // 检查令牌是否过期
    if (accountService.isTokenExpired(accountId)) {
      return { success: false, error: 'Account token has expired, please log in again' }
    }
    
    // 设置令牌到认证服务
    authService.setAccessToken(tokens.accessToken)
    
    // 同步更新配置中的当前账号ID
    configService.updateConfig({ currentAccountId: accountId })
    
    const account = accountService.getAccount(accountId)
    return { 
      success: true, 
      account: account?.accountInfo,
      accessToken: tokens.accessToken
    }
  } catch (error) {
    console.error('Failed to switch account:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Failed to switch account' }
  }
})

ipcMain.handle('accounts:remove', async (event, accountId: string) => {
  try {
    const accountToRemove = accountService.getAccount(accountId)
    const success = accountService.removeAccount(accountId)
    if (!success) {
      return { success: false, error: 'Account not found' }
    }
    
    // 如果删除的是活跃账号，清除当前账号ID并退出登录
    if (accountToRemove?.accountInfo.isActive) {
      configService.updateConfig({ currentAccountId: undefined })
      await authService.logout()
    }
    
    return { success: true }
  } catch (error) {
    console.error('Failed to remove account:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Failed to remove account' }
  }
})

ipcMain.handle('accounts:clear-all', async () => {
  try {
    accountService.clearAllAccounts()
    configService.updateConfig({ currentAccountId: undefined })
    await authService.logout()
    console.log('All accounts cleared successfully')
    return { success: true }
  } catch (error) {
    console.error('Failed to clear all accounts:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Failed to clear all accounts' }
  }
})


// In this file you can include the rest of your app's main process code. 