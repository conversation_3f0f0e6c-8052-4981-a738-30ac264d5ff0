// 默认配置 - 运行时从用户数据目录加载实际配置
export const DEFAULT_APP_CONFIG = {
  // Microsoft App Registration details - 用户运行时配置
  // 配置文件保存在用户数据目录：C:\Users\<USER>\AppData\Roaming\od-commander\config.json
  auth: {
    clientId: '14d82eec-204b-4c2f-b7e8-296a70dab67e', // 暂时使用原来的Graph Explorer ID让登录正常工作
    clientSecret: '', // 将在首次运行时要求用户输入
    authority: 'https://login.microsoftonline.com/common',
    redirectUri: 'http://localhost'
  },
  
  // Application settings
  app: {
    name: 'OD-Commander',
    version: '1.0.0',
    description: 'A modern OneDrive client for Windows'
  },
  
  // OneDrive API scopes - 添加了offline_access以获取长期refresh token
  scopes: [
    'https://graph.microsoft.com/Files.ReadWrite.All',
    'https://graph.microsoft.com/User.Read',
    'offline_access' // 关键：启用长期授权
  ]
}

// 🔧 **OneDrive Commander Azure 应用注册配置指南**
/*
⚠️  **重要：必须完成Azure应用注册才能使用本应用**

📋 **步骤1：创建Azure应用注册**
1. 访问并登录 Microsoft Azure：
   https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationsListBlade

2. 点击"新注册"并填写信息：
   - 名称：OneDrive Commander (或您喜欢的名称)
   - 支持的账户类型：任何组织目录(任何 Azure AD 目录 - 多租户)中的账户和个人 Microsoft 账户
   - 重定向URI：选择"Web"，输入：http://localhost:3000/auth/callback

3. 点击"注册"

📋 **步骤2：获取客户端ID**
1. 在应用主页复制"应用程序(客户端) ID"
2. 将此ID粘贴到上方 clientId 字段

📋 **步骤3：创建客户端密码**
1. 在左侧菜单选择"证书和密码"
2. 点击"新客户端密码"
3. 填写描述(如：OneDrive Commander Secret)，选择过期时间(建议24个月)
4. 点击"添加"
5. 复制生成的"值"(注意：只显示一次!)
6. 将此值粘贴到上方 clientSecret 字段

📋 **步骤4：配置API权限**
1. 在左侧菜单选择"API权限"
2. 点击"添加权限" → "Microsoft Graph" → "委托的权限"
3. 勾选以下权限：
   ✅ Files.ReadWrite.All (读写用户文件)
   ✅ offline_access (离线访问，获取90天refresh token)
   ✅ User.Read (读取用户基本信息)
4. 点击"添加权限"
5. 点击"为 [租户] 授予管理员同意"(如果您是管理员)

📋 **步骤5：配置身份验证**
1. 在左侧菜单选择"身份验证"
2. 确认重定向URI包含：http://localhost:3000/auth/callback
3. 在"隐式授权和混合流"下启用：
   ✅ 访问令牌
   ✅ ID 令牌

🎯 **配置完成后的优势**：
- ✅ 真正的90天refresh token有效期
- ✅ 安全的Authorization Code Flow with PKCE
- ✅ 自动后台token刷新
- ✅ 企业级安全性和合规性
- ✅ 无需频繁重新登录

📝 **配置示例**：
auth: {
  clientId: '12345678-1234-1234-1234-123456789012',
  clientSecret: 'your-secret-value-here',
  authority: 'https://login.microsoftonline.com/common',
  redirectUri: 'http://localhost:3000/auth/callback'
}
*/ 