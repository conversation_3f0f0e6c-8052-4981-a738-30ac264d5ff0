import { create } from 'zustand'

export interface AccountInfo {
  id: string
  displayName: string
  userPrincipalName: string
  mail: string
  jobTitle?: string
  officeLocation?: string
  businessPhones: string[]
  mobilePhone?: string
  photo?: string
  driveType?: string
  lastLoginTime: number
  isActive: boolean
}

interface AccountState {
  accounts: AccountInfo[]
  activeAccount: AccountInfo | null
  isLoading: boolean
  error: string | null
  
  // Actions
  loadAccounts: () => Promise<void>
  loadActiveAccount: () => Promise<void>
  switchAccount: (accountId: string) => Promise<boolean>
  removeAccount: (accountId: string) => Promise<boolean>
  clearAllAccounts: () => Promise<boolean>
  refreshAccounts: () => Promise<void>
}

export const useAccountStore = create<AccountState>((set, get) => ({
  accounts: [],
  activeAccount: null,
  isLoading: false,
  error: null,

  loadAccounts: async () => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.accounts.getAll()
      if (result.success) {
        set({ 
          accounts: result.accounts || [], 
          isLoading: false 
        })
      } else {
        throw new Error(result.error || '获取账号列表失败')
      }
    } catch (error) {
      console.error('加载账号列表失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '加载账号列表失败', 
        isLoading: false 
      })
    }
  },

  loadActiveAccount: async () => {
    try {
      const result = await window.electronAPI.accounts.getActive()
      if (result.success) {
        set({ activeAccount: result.account })
      } else {
        set({ activeAccount: null })
      }
    } catch (error) {
      console.error('获取活跃账号失败:', error)
      set({ activeAccount: null })
    }
  },

  switchAccount: async (accountId: string) => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.accounts.switch(accountId)
      if (result.success) {
        // 刷新账号列表和活跃账号
        await get().loadAccounts()
        await get().loadActiveAccount()
        set({ isLoading: false })
        return true
      } else {
        throw new Error(result.error || '切换账号失败')
      }
    } catch (error) {
      console.error('切换账号失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '切换账号失败', 
        isLoading: false 
      })
      return false
    }
  },

  removeAccount: async (accountId: string) => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.accounts.remove(accountId)
      if (result.success) {
        // 刷新账号列表和活跃账号
        await get().loadAccounts()
        await get().loadActiveAccount()
        set({ isLoading: false })
        return true
      } else {
        throw new Error(result.error || '删除账号失败')
      }
    } catch (error) {
      console.error('删除账号失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '删除账号失败', 
        isLoading: false 
      })
      return false
    }
  },

  clearAllAccounts: async () => {
    set({ isLoading: true, error: null })
    try {
      const result = await window.electronAPI.accounts.clearAll()
      if (result.success) {
        set({ 
          accounts: [], 
          activeAccount: null, 
          isLoading: false 
        })
        return true
      } else {
        throw new Error(result.error || '清除所有账号失败')
      }
    } catch (error) {
      console.error('清除所有账号失败:', error)
      set({ 
        error: error instanceof Error ? error.message : '清除所有账号失败', 
        isLoading: false 
      })
      return false
    }
  },

  refreshAccounts: async () => {
    await get().loadAccounts()
    await get().loadActiveAccount()
  }
}))

// 格式化最后登录时间
export const formatLastLoginTime = (timestamp: number): string => {
  const now = Date.now()
  const diff = now - timestamp
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return new Date(timestamp).toLocaleDateString()
  }
} 